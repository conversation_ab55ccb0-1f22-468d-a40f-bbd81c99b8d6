package com.haoys.user.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.SecureTokenVo;
import com.haoys.user.model.SecureAppRequestLog;
import com.haoys.user.vo.SystemUserInfoExtendVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 增强的安全Token服务测试类
 * 测试用户验证和操作记录功能
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class SecureTokenEnhancedServiceTest {
    
    @Autowired
    private SecureTokenService secureTokenService;
    
    @Autowired
    private SecureAppRequestLogService secureAppRequestLogService;
    
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    
    @Test
    public void testGenerateCodeWithValidUser() {
        log.info("=== 测试生成Code（有效用户） ===");
        
        SecureTokenParam.GenerateCodeParam param = new SecureTokenParam.GenerateCodeParam();
        param.setAppId("edc_app_local");
        param.setAppSecret("edc_secret_local_2025_local123456");
        param.setUserId("1"); // 假设用户ID为1存在
        param.setExtraInfo("测试有效用户");
        param.setEnvironment("local");
        
        try {
            SecureTokenVo.CodeResponse response = secureTokenService.generateCode(param);
            
            assertNotNull(response);
            assertNotNull(response.getCode());
            assertNotNull(response.getRefreshCode());
            assertNotNull(response.getExpireTime());
            assertTrue(response.getExpiresIn() > 0);
            
            log.info("生成Code成功: code={}, refreshCode={}", response.getCode(), response.getRefreshCode());
            
            // 验证操作记录是否生成
            verifyRequestLog(param.getAppId(), "local", "generate_code", "success");
            
        } catch (Exception e) {
            log.error("测试失败: {}", e.getMessage(), e);
            fail("生成Code失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testGenerateCodeWithInvalidUser() {
        log.info("=== 测试生成Code（无效用户） ===");
        
        SecureTokenParam.GenerateCodeParam param = new SecureTokenParam.GenerateCodeParam();
        param.setAppId("edc_app_local");
        param.setAppSecret("edc_secret_local_2025_local123456");
        param.setUserId("999999"); // 不存在的用户ID
        param.setExtraInfo("测试无效用户");
        param.setEnvironment("local");
        
        Exception exception = assertThrows(RuntimeException.class, () -> {
            secureTokenService.generateCode(param);
        });
        
        assertTrue(exception.getMessage().contains("用户ID不存在") || 
                  exception.getMessage().contains("用户验证失败"));
        
        log.info("预期的异常: {}", exception.getMessage());
        
        // 验证失败的操作记录是否生成
        verifyRequestLog(param.getAppId(), "local", "generate_code", "failed");
    }
    
    @Test
    public void testGenerateCodeWithoutUser() {
        log.info("=== 测试生成Code（不提供用户ID） ===");
        
        SecureTokenParam.GenerateCodeParam param = new SecureTokenParam.GenerateCodeParam();
        param.setAppId("edc_app_local");
        param.setAppSecret("edc_secret_local_2025_local123456");
        param.setUserId(null); // 不提供用户ID
        param.setExtraInfo("测试不提供用户ID");
        param.setEnvironment("local");
        
        try {
            SecureTokenVo.CodeResponse response = secureTokenService.generateCode(param);
            
            assertNotNull(response);
            assertNotNull(response.getCode());
            assertNotNull(response.getRefreshCode());
            
            log.info("生成Code成功（无用户ID）: code={}", response.getCode());
            
            // 验证操作记录是否生成
            verifyRequestLog(param.getAppId(), "local", "generate_code", "success");
            
        } catch (Exception e) {
            log.error("测试失败: {}", e.getMessage(), e);
            fail("生成Code失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testGenerateCodeWithInvalidCredentials() {
        log.info("=== 测试生成Code（无效凭证） ===");
        
        SecureTokenParam.GenerateCodeParam param = new SecureTokenParam.GenerateCodeParam();
        param.setAppId("invalid_app_id");
        param.setAppSecret("invalid_secret");
        param.setUserId("1");
        param.setExtraInfo("测试无效凭证");
        param.setEnvironment("local");
        
        Exception exception = assertThrows(RuntimeException.class, () -> {
            secureTokenService.generateCode(param);
        });
        
        assertTrue(exception.getMessage().contains("应用凭证验证失败"));
        
        log.info("预期的异常: {}", exception.getMessage());
        
        // 验证失败的操作记录是否生成
        verifyRequestLog(param.getAppId(), "local", "generate_code", "failed");
    }
    
    @Test
    public void testUserValidation() {
        log.info("=== 测试用户验证功能 ===");
        
        // 测试有效用户
        try {
            SystemUserInfoExtendVo userInfo = systemUserInfoService.getSystemUserInfoByUserId("1");
            if (userInfo != null) {
                log.info("用户验证成功: userId=1, username={}", userInfo.getUsername());
            } else {
                log.warn("用户ID=1不存在，请确保测试数据正确");
            }
        } catch (Exception e) {
            log.error("用户验证异常: {}", e.getMessage());
        }
        
        // 测试无效用户
        try {
            SystemUserInfoExtendVo userInfo = systemUserInfoService.getSystemUserInfoByUserId("999999");
            assertNull(userInfo, "不存在的用户应该返回null");
            log.info("无效用户验证正确: userId=999999 返回null");
        } catch (Exception e) {
            log.error("无效用户验证异常: {}", e.getMessage());
        }
    }
    
    @Test
    public void testRequestLogService() {
        log.info("=== 测试请求日志服务 ===");
        
        String appId = "test_app";
        String environment = "test";
        String requestType = "test_request";
        String clientIp = "127.0.0.1";
        String userAgent = "Test-Agent";
        String requestData = "{\"test\":\"data\"}";
        String responseData = "{\"result\":\"success\"}";
        
        // 测试记录成功请求
        secureAppRequestLogService.logSuccessRequest(
            appId, environment, requestType, clientIp, userAgent, requestData, responseData);
        
        // 测试记录失败请求
        secureAppRequestLogService.logFailedRequest(
            appId, environment, requestType, clientIp, userAgent, requestData, "测试错误");
        
        // 查询日志记录
        List<SecureAppRequestLog> logs = secureAppRequestLogService.getLogsByCondition(
            appId, environment, requestType, LocalDateTime.now().minusHours(1), LocalDateTime.now());
        
        assertNotNull(logs);
        assertTrue(logs.size() >= 2, "应该至少有2条日志记录");
        
        log.info("日志记录测试成功，共找到{}条记录", logs.size());
        
        for (SecureAppRequestLog logRecord : logs) {
            log.info("日志记录: appId={}, requestType={}, responseStatus={}, requestTime={}", 
                    logRecord.getAppId(), logRecord.getRequestType(), 
                    logRecord.getResponseStatus(), logRecord.getRequestTime());
        }
    }
    
    /**
     * 验证请求日志是否正确记录
     */
    private void verifyRequestLog(String appId, String environment, String requestType, String expectedStatus) {
        try {
            // 等待一下，确保日志记录完成
            Thread.sleep(100);
            
            List<SecureAppRequestLog> logs = secureAppRequestLogService.getLogsByCondition(
                appId, environment, requestType, LocalDateTime.now().minusMinutes(1), LocalDateTime.now());
            
            assertNotNull(logs, "日志记录不应为null");
            assertTrue(logs.size() > 0, "应该有日志记录");
            
            // 查找最新的日志记录
            SecureAppRequestLog latestLog = logs.stream()
                .max((log1, log2) -> log1.getRequestTime().compareTo(log2.getRequestTime()))
                .orElse(null);
            
            assertNotNull(latestLog, "应该找到最新的日志记录");
            assertEquals(expectedStatus, latestLog.getResponseStatus(), "日志状态应该匹配");
            
            log.info("日志验证成功: appId={}, requestType={}, status={}", 
                    latestLog.getAppId(), latestLog.getRequestType(), latestLog.getResponseStatus());
            
        } catch (Exception e) {
            log.warn("日志验证失败: {}", e.getMessage());
        }
    }
}
