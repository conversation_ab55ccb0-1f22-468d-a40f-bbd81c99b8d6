package com.haoys.user.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.google.common.collect.Lists;
import com.haoys.user.security.interceptor.AccessTokenValidationInterceptor;
import com.haoys.user.security.interceptor.RepeatSubmitInterceptor;
import com.haoys.user.security.interceptor.SystemLicenseInterceptor;
import com.haoys.user.storge.cloud.OssStorageConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Iterator;
import java.util.List;
import java.util.TimeZone;

/**
 * web mvc 配置
 */
@Configuration
@EnableWebMvc
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private OssStorageConfig storageConfig;
    @Autowired
    private RepeatSubmitInterceptor repeatSubmitInterceptor;
    @Autowired
    private SystemLicenseInterceptor systemLicenseInterceptor;
    @Autowired
    private AccessTokenValidationInterceptor accessTokenValidationInterceptor;

        /**
     * html静态资源   js静态资源    css静态资源
     */
    private final List<String> staticResources = Lists.newArrayList("/**/*.html",
            "/**/*.js",
            "/**/*.css",
            "/**/*.woff",
            "/**/*.xlsx",
            "/**/*.ttf");

    @Bean
    public HttpMessageConverter<String> responseBody(){
        return new StringHttpMessageConverter(Charset.forName("UTF-8"));
    }

    /**
     * 配置本地文件上传的虚拟路径和静态化的文件生成路径
     * 备注：这是一种图片上传访问图片的方法，实际上也可以使用nginx反向代理访问图片
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 文件上传
        String uploadFolder = storageConfig.getUploadFolder();
        uploadFolder = StringUtils.appendIfMissing(uploadFolder, File.separator);
        registry.addResourceHandler(storageConfig.getAccessPathPattern() + "/**")
                .addResourceLocations("file:" + uploadFolder);

        // 配置系统监控模板资源访问
        registry.addResourceHandler("/templates/system-monitor/**")
                .addResourceLocations("classpath:/templates/system-monitor/");

        // 配置AI聊天模板资源访问
        registry.addResourceHandler("/templates/ai-chat/**")
                .addResourceLocations("classpath:/templates/ai-chat/");

        //这句不要忘了，否则项目默认静态资源映射会失效
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        // swagger 配置
        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 系统许可证验证拦截器 - 优先级最高
        registry.addInterceptor(systemLicenseInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(staticResources)
                .order(1);

        // AccessToken验证拦截器 - 安全验证
        registry.addInterceptor(accessTokenValidationInterceptor)
                .addPathPatterns("/api/secure/token/**")
                .order(2);

        // 防重复提交拦截器
        registry.addInterceptor(repeatSubmitInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(staticResources)
                .order(3);
    }
    
    /*@Override
    public void configureViewResolvers(ViewResolverRegistry registry) {
        registry.viewResolver(new InternalResourceViewResolver("/resources/views/", ".html"));
    }*/
    
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        Iterator<HttpMessageConverter<?>> iterator = converters.iterator();
        while (iterator.hasNext()) {
            HttpMessageConverter<?> converter = iterator.next();
            if (converter instanceof MappingJackson2HttpMessageConverter) {
                iterator.remove();
            }
        }
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        MappingJackson2XmlHttpMessageConverter mappingJackson2XmlHttpMessageConverter = new MappingJackson2XmlHttpMessageConverter();
        //设置转换器格式，和时区。
        Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder()
                .indentOutput(true)
                .dateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
                .timeZone(TimeZone.getTimeZone("Asia/Shanghai"))
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                //.serializationInclusion(JsonInclude.Include.NON_EMPTY)
                .failOnEmptyBeans(false)
                .modulesToInstall(new ParameterNamesModule());
        mappingJackson2HttpMessageConverter.setObjectMapper(builder.build());
        /*mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Lists.newArrayList(MediaType.APPLICATION_JSON,MediaType.APPLICATION_OCTET_STREAM,
                MediaType.APPLICATION_JSON_UTF8,MediaType.APPLICATION_FORM_URLENCODED,MediaType.TEXT_HTML,MediaType.TEXT_PLAIN));*/
        mappingJackson2HttpMessageConverter.setDefaultCharset(Charset.forName("UTF-8"));
        mappingJackson2HttpMessageConverter.setPrettyPrint(true);
        converters.add(mappingJackson2HttpMessageConverter);
        
        /*mappingJackson2XmlHttpMessageConverter.setSupportedMediaTypes(Lists.newArrayList(MediaType.APPLICATION_XML,MediaType.APPLICATION_OCTET_STREAM));
        mappingJackson2XmlHttpMessageConverter.setObjectMapper(builder.createXmlMapper(true).build());
        mappingJackson2XmlHttpMessageConverter.setDefaultCharset(Charset.forName("UTF-8"));
        mappingJackson2XmlHttpMessageConverter.setPrettyPrint(true);
        converters.add(mappingJackson2XmlHttpMessageConverter);*/

        // 清除默认 Json 转换器
       // converters.removeIf(converter -> converter instanceof MappingJackson2HttpMessageConverter);

        // 配置 FastJson
        /*FastJsonConfig config = new FastJsonConfig();
        config.setSerializerFeatures(SerializerFeature.QuoteFieldNames,
                SerializerFeature.WriteEnumUsingToString,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteDateUseDateFormat,
                SerializerFeature.BrowserCompatible,
                SerializerFeature.DisableCircularReferenceDetect);*/
        // 解决long型JS精度丢失问题
        /*SerializeConfig serializeConfig = SerializeConfig.globalInstance;
        serializeConfig.put(Long.class, ToStringSerializer.instance);
        serializeConfig.put(Long.TYPE, ToStringSerializer.instance);
        config.setSerializeConfig(serializeConfig);*/

        // 添加 FastJsonHttpMessageConverter
        /*FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        fastJsonHttpMessageConverter.setFastJsonConfig(config);
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        fastJsonHttpMessageConverter.setSupportedMediaTypes(fastMediaTypes);
        converters.add(fastJsonHttpMessageConverter);

        FastJsonHttpMessageConverter fastJsonConverter = new FastJsonHttpMessageConverter();
        //FastJsonConfig config = new FastJsonConfig();
        config.setCharset(Charset.forName("UTF-8"));
        config.setDateFormat("yyyy-MM-dd HH:mm:ss");
        config.setSerializerFeatures(SerializerFeature.WriteMapNullValue);
        fastJsonConverter.setFastJsonConfig(config);
        List<MediaType> list = new ArrayList<>();
        list.add(MediaType.APPLICATION_JSON);
        fastJsonConverter.setSupportedMediaTypes(list);
        converters.add(fastJsonConverter);*/
    }
}