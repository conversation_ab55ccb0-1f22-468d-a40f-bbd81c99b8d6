package com.haoys.user.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.config.SecureTokenConfig;
import com.haoys.user.domain.dto.SecureTokenDto;
import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.SecureTokenVo;
import com.haoys.user.model.SecureAppConfig;
import com.haoys.user.service.SecureAppConfigService;
import com.haoys.user.service.SecureAppRequestLogService;
import com.haoys.user.service.SecureTokenService;
import com.haoys.user.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

/**
 * 安全Token管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Service
public class SecureTokenServiceImpl implements SecureTokenService {
    
    @Autowired
    private SecureTokenConfig secureTokenConfig;

    @Autowired
    private RedisTemplateService redisTemplateService;

    /**
     * 配置文件中的AccessToken有效期（秒）
     * 如果配置了此值，将覆盖数据库中的配置
     */
    @Value("${secure-token.access-token-expiration:#{null}}")
    private Integer configAccessTokenExpiration;

    @Autowired
    private SecureAppConfigService secureAppConfigService;

    @Autowired
    private SecureAppRequestLogService secureAppRequestLogService;

    private static final String CODE_PREFIX = "code:";
    private static final String REFRESH_PREFIX = "refresh:";

    /**
     * 获取实际的AccessToken有效期
     * 优先使用配置文件中的值，如果没有配置则使用数据库中的值
     */
    private Integer getActualAccessTokenExpiration(SecureAppConfig appConfig) {
        if (configAccessTokenExpiration != null && configAccessTokenExpiration > 0) {
            log.debug("使用配置文件中的AccessToken有效期: {}秒", configAccessTokenExpiration);
            return configAccessTokenExpiration;
        } else {
            log.debug("使用数据库中的AccessToken有效期: {}秒", appConfig.getAccessTokenExpiration());
            return appConfig.getAccessTokenExpiration();
        }
    }
    private static final String ACCESS_PREFIX = "access:";
    
    @Override
    public SecureTokenVo.CodeResponse generateCode(SecureTokenParam.GenerateCodeParam param) {
        String clientIp = RequestUtil.getClientIp();
        String userAgent = RequestUtil.getUserAgent();
        String requestData = JSON.toJSONString(param);

        if (!secureTokenConfig.getEnabled()) {
            String errorMsg = "安全Token功能未启用";
            secureAppRequestLogService.logFailedRequest(
                param != null ? param.getAppId() : "unknown",
                param != null ? param.getEnvironment() : "unknown",
                "generate_code", clientIp, userAgent, requestData, errorMsg);
            throw new RuntimeException(errorMsg);
        }

        if (param == null) {
            String errorMsg = "请求参数不能为空";
            secureAppRequestLogService.logFailedRequest(
                "unknown", "unknown", "generate_code", clientIp, userAgent, "null", errorMsg);
            throw new RuntimeException(errorMsg);
        }

        // 验证AppId和AppSecret
        String environment = StrUtil.isBlank(param.getEnvironment()) ?
                secureAppConfigService.getCurrentEnvironment() : param.getEnvironment();

        SecureAppConfig appConfig = secureAppConfigService.validateAppCredentials(
                param.getAppId(), param.getAppSecret(), environment);

        if (appConfig == null) {
            String errorMsg = "应用凭证验证失败";
            secureAppRequestLogService.logFailedRequest(
                param.getAppId(), environment, "generate_code", clientIp, userAgent, requestData, errorMsg);
            throw new RuntimeException(errorMsg);
        }

        // 验证用户ID是否存在（如果提供了userId）
        if (StrUtil.isNotBlank(param.getUserId())) {
            try {
                // 简单的用户ID格式验证
                Long userId = Long.parseLong(param.getUserId());
                if (userId <= 0) {
                    String errorMsg = "用户ID格式无效: " + param.getUserId();
                    secureAppRequestLogService.logFailedRequest(
                        param.getAppId(), environment, "generate_code", clientIp, userAgent, requestData, errorMsg);
                    throw new RuntimeException(errorMsg);
                }
                log.info("用户ID格式验证成功: userId={}", param.getUserId());
            } catch (NumberFormatException e) {
                String errorMsg = "用户ID格式无效: " + param.getUserId();
                secureAppRequestLogService.logFailedRequest(
                    param.getAppId(), environment, "generate_code", clientIp, userAgent, requestData, errorMsg);
                throw new RuntimeException(errorMsg);
            } catch (Exception e) {
                String errorMsg = "用户验证失败: " + e.getMessage();
                secureAppRequestLogService.logFailedRequest(
                    param.getAppId(), environment, "generate_code", clientIp, userAgent, requestData, errorMsg);
                throw new RuntimeException(errorMsg);
            }
        }

        // 检查每日请求限制
        if (!secureAppConfigService.checkDailyRequestLimit(param.getAppId(), environment)) {
            String errorMsg = "今日请求次数已达上限";
            secureAppRequestLogService.logFailedRequest(
                param.getAppId(), environment, "generate_code", clientIp, userAgent, requestData, errorMsg);
            throw new RuntimeException(errorMsg);
        }

        // 生成唯一的code和refreshCode
        String code = generateUniqueCode();
        String refreshCode = generateUniqueRefreshCode();

        // 创建Token数据
        SecureTokenDto tokenDto = new SecureTokenDto()
                .setAppId(param.getAppId())
                .setEnvironment(environment)
                .setCode(code)
                .setRefreshCode(refreshCode)
                .setCreateTime(LocalDateTime.now())
                .setExpireTime(LocalDateTime.now().plusSeconds(appConfig.getCodeExpiration()))
                .setUsed(false)
                .setUserId(param.getUserId())
                .setExtraInfo(param.getExtraInfo());

        // 存储到Redis
        String codeKey = secureTokenConfig.getRedisPrefix() + CODE_PREFIX + code;
        String refreshKey = secureTokenConfig.getRedisPrefix() + REFRESH_PREFIX + refreshCode;

        redisTemplateService.set(codeKey, JSON.toJSONString(tokenDto), appConfig.getCodeExpiration().longValue());
        redisTemplateService.set(refreshKey, JSON.toJSONString(tokenDto), appConfig.getCodeExpiration().longValue());

        // 创建响应对象
        SecureTokenVo.CodeResponse response = new SecureTokenVo.CodeResponse()
                .setCode(code)
                .setRefreshCode(refreshCode)
                .setExpireTime(tokenDto.getExpireTime())
                .setExpiresIn(appConfig.getCodeExpiration().longValue());

        // 记录成功的操作日志
        String responseData = JSON.toJSONString(response);
        secureAppRequestLogService.logSuccessRequest(
            param.getAppId(), environment, "generate_code", clientIp, userAgent, requestData, responseData);

        log.info("生成Code成功: appId={}, code={}, refreshCode={}, userId={}, environment={}",
                param.getAppId(), code, refreshCode, tokenDto.getUserId(), environment);

        return response;
    }
    
    @Override
    public SecureTokenVo.AccessTokenResponse getAccessToken(SecureTokenParam.GetAccessTokenParam param) {
        if (!secureTokenConfig.getEnabled()) {
            throw new RuntimeException("安全Token功能未启用");
        }

        if (param == null) {
            throw new RuntimeException("请求参数不能为空");
        }

        String code = param.getCode();
        String refreshCode = param.getRefreshCode();

        if (StrUtil.isBlank(code)) {
            throw new RuntimeException("Code不能为空");
        }
        if (StrUtil.isBlank(refreshCode)) {
            throw new RuntimeException("RefreshCode不能为空");
        }

        // 验证Code的合法性
        String codeKey = secureTokenConfig.getRedisPrefix() + CODE_PREFIX + code;
        Object codeData = redisTemplateService.get(codeKey);

        if (codeData == null) {
            throw new RuntimeException("Code无效或已过期");
        }

        SecureTokenDto codeTokenDto = JSON.parseObject(codeData.toString(), SecureTokenDto.class);

        // 验证RefreshCode的合法性
        String refreshKey = secureTokenConfig.getRedisPrefix() + REFRESH_PREFIX + refreshCode;
        Object refreshData = redisTemplateService.get(refreshKey);

        if (refreshData == null) {
            throw new RuntimeException("RefreshCode无效或已过期");
        }

        SecureTokenDto refreshTokenDto = JSON.parseObject(refreshData.toString(), SecureTokenDto.class);

        // 验证Code和RefreshCode是否匹配（来自同一次生成）
        if (!code.equals(codeTokenDto.getCode()) || !refreshCode.equals(refreshTokenDto.getRefreshCode())) {
            throw new RuntimeException("Code和RefreshCode不匹配");
        }

        // 验证Code和RefreshCode的一致性
        if (!codeTokenDto.getAppId().equals(refreshTokenDto.getAppId()) ||
            !codeTokenDto.getEnvironment().equals(refreshTokenDto.getEnvironment())) {
            throw new RuntimeException("Code和RefreshCode不一致");
        }

        // 检查是否已使用
        if (Boolean.TRUE.equals(refreshTokenDto.getUsed())) {
            throw new RuntimeException("RefreshCode已被使用");
        }

        // 检查是否过期
        if (refreshTokenDto.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("RefreshCode已过期");
        }

        // 获取应用配置
        SecureAppConfig appConfig = secureAppConfigService.getByAppIdAndEnvironment(
                refreshTokenDto.getAppId(), refreshTokenDto.getEnvironment());

        if (appConfig == null) {
            throw new RuntimeException("应用配置不存在");
        }

        // 获取实际的AccessToken有效期
        Integer actualAccessTokenExpiration = getActualAccessTokenExpiration(appConfig);

        // 生成AccessToken
        String accessToken = generateUniqueAccessToken();

        // 更新Token数据
        refreshTokenDto.setAccessToken(accessToken)
                .setUsed(true)
                .setUsedTime(LocalDateTime.now())
                .setExpireTime(LocalDateTime.now().plusSeconds(actualAccessTokenExpiration));

        // 存储AccessToken到Redis
        String accessKey = secureTokenConfig.getRedisPrefix() + ACCESS_PREFIX + accessToken;
        redisTemplateService.set(accessKey, JSON.toJSONString(refreshTokenDto), actualAccessTokenExpiration.longValue());

        log.info("生成AccessToken成功: appId={}, environment={}, 有效期={}秒 (配置来源: {})",
                appConfig.getAppId(), appConfig.getEnvironment(), actualAccessTokenExpiration,
                configAccessTokenExpiration != null ? "配置文件" : "数据库");

        // 删除已使用的code和refreshCode
        redisTemplateService.del(codeKey);
        redisTemplateService.del(refreshKey);

        log.info("生成AccessToken成功: appId={}, accessToken={}, userId={}, environment={}",
                refreshTokenDto.getAppId(), accessToken, refreshTokenDto.getUserId(), refreshTokenDto.getEnvironment());

        return new SecureTokenVo.AccessTokenResponse()
                .setAccessToken(accessToken)
                .setExpireTime(refreshTokenDto.getExpireTime())
                .setExpiresIn(actualAccessTokenExpiration.longValue())
                .setUserId(refreshTokenDto.getUserId());
    }

    @Override
    public SecureTokenVo.ValidateResponse validateAccessToken(String accessToken) {
        if (!secureTokenConfig.getEnabled()) {
            throw new RuntimeException("安全Token功能未启用");
        }

        if (StrUtil.isBlank(accessToken)) {
            return new SecureTokenVo.ValidateResponse()
                    .setValid(false)
                    .setRemainingTime(0L);
        }

        // 从Redis获取Token数据
        String accessKey = secureTokenConfig.getRedisPrefix() + ACCESS_PREFIX + accessToken;
        Object tokenData = redisTemplateService.get(accessKey);

        if (tokenData == null) {
            return new SecureTokenVo.ValidateResponse()
                    .setValid(false)
                    .setRemainingTime(0L);
        }

        SecureTokenDto tokenDto = JSON.parseObject(tokenData.toString(), SecureTokenDto.class);

        // 检查是否过期
        LocalDateTime now = LocalDateTime.now();
        if (tokenDto.getExpireTime().isBefore(now)) {
            // 删除过期的token
            redisTemplateService.del(accessKey);
            return new SecureTokenVo.ValidateResponse()
                    .setValid(false)
                    .setRemainingTime(0L);
        }

        // 计算剩余时间
        long remainingTime = tokenDto.getExpireTime().toEpochSecond(ZoneOffset.UTC) - now.toEpochSecond(ZoneOffset.UTC);

        return new SecureTokenVo.ValidateResponse()
                .setValid(true)
                .setUserId(tokenDto.getUserId())
                .setRemainingTime(remainingTime)
                .setExtraInfo(tokenDto.getExtraInfo());
    }

    @Override
    public SecureTokenVo.AccessTokenResponse refreshAccessToken(String accessToken) {
        if (!secureTokenConfig.getEnabled()) {
            throw new RuntimeException("安全Token功能未启用");
        }

        SecureTokenVo.ValidateResponse validateResponse = validateAccessToken(accessToken);
        if (!validateResponse.getValid()) {
            throw new RuntimeException("AccessToken无效或已过期");
        }

        // 生成新的AccessToken
        String newAccessToken = generateUniqueAccessToken();

        // 获取原Token数据
        String accessKey = secureTokenConfig.getRedisPrefix() + ACCESS_PREFIX + accessToken;
        Object tokenData = redisTemplateService.get(accessKey);
        SecureTokenDto tokenDto = JSON.parseObject(tokenData.toString(), SecureTokenDto.class);

        // 更新Token数据
        tokenDto.setAccessToken(newAccessToken)
                .setExpireTime(LocalDateTime.now().plusSeconds(secureTokenConfig.getAccessTokenExpiration()));

        // 存储新的AccessToken
        String newAccessKey = secureTokenConfig.getRedisPrefix() + ACCESS_PREFIX + newAccessToken;
        redisTemplateService.set(newAccessKey, JSON.toJSONString(tokenDto), secureTokenConfig.getAccessTokenExpiration());

        // 删除旧的AccessToken
        redisTemplateService.del(accessKey);

        log.info("刷新AccessToken成功: oldToken={}, newToken={}, userId={}", accessToken, newAccessToken, tokenDto.getUserId());

        return new SecureTokenVo.AccessTokenResponse()
                .setAccessToken(newAccessToken)
                .setExpireTime(tokenDto.getExpireTime())
                .setExpiresIn(secureTokenConfig.getAccessTokenExpiration())
                .setUserId(tokenDto.getUserId());
    }

    @Override
    public Boolean revokeAccessToken(String accessToken) {
        if (!secureTokenConfig.getEnabled()) {
            return false;
        }

        if (StrUtil.isBlank(accessToken)) {
            return false;
        }

        String accessKey = secureTokenConfig.getRedisPrefix() + ACCESS_PREFIX + accessToken;
        Boolean result = redisTemplateService.del(accessKey);

        log.info("撤销AccessToken: accessToken={}, result={}", accessToken, result);
        return result;
    }

    @Override
    public Long cleanExpiredTokens() {
        // Redis的TTL机制会自动清理过期的key，这里主要用于统计和日志
        log.info("清理过期Token任务执行完成");
        return 0L;
    }

    /**
     * 生成唯一的Code
     */
    private String generateUniqueCode() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = IdUtil.fastSimpleUUID().substring(0, 8);
        String signature = DigestUtil.md5Hex(timestamp + uuid + secureTokenConfig.getSecretKey()).substring(0, 8);
        return "code_" + timestamp.substring(timestamp.length() - 6) + "_" + uuid + "_" + signature;
    }

    /**
     * 生成唯一的RefreshCode
     */
    private String generateUniqueRefreshCode() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = IdUtil.fastSimpleUUID().substring(0, 12);
        String signature = DigestUtil.md5Hex(timestamp + uuid + secureTokenConfig.getSecretKey()).substring(0, 8);
        return "refresh_" + timestamp.substring(timestamp.length() - 6) + "_" + uuid + "_" + signature;
    }

    /**
     * 生成唯一的AccessToken
     */
    private String generateUniqueAccessToken() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = IdUtil.fastSimpleUUID().substring(0, 16);
        String signature = DigestUtil.md5Hex(timestamp + uuid + secureTokenConfig.getSecretKey()).substring(0, 12);
        return "access_" + timestamp.substring(timestamp.length() - 6) + "_" + uuid + "_" + signature;
    }
}
